const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const db = require('./db');

async function checkSchema() {
    console.log('🔍 Checking database schema...\n');
    
    if (!process.env.DATABASE_URL) {
        console.error('ERROR: DATABASE_URL is not set in backend/.env');
        process.exit(1);
    }

    try {
        // Check if user_playlists table exists and get its structure
        console.log('📋 Checking user_playlists table structure...');
        const tableInfo = await db.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'user_playlists' 
            AND table_schema = 'public'
            ORDER BY ordinal_position
        `);

        if (tableInfo.rowCount === 0) {
            console.log('❌ user_playlists table does not exist');
        } else {
            console.log('✅ user_playlists table exists with columns:');
            tableInfo.rows.forEach(col => {
                console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
            });
            
            // Check specifically for updated_at column
            const hasUpdatedAt = tableInfo.rows.some(col => col.column_name === 'updated_at');
            console.log(`\n🎯 updated_at column: ${hasUpdatedAt ? '✅ EXISTS' : '❌ MISSING'}`);
        }

        // Check if the trigger exists
        console.log('\n🔧 Checking for update trigger...');
        const triggerInfo = await db.query(`
            SELECT trigger_name, event_manipulation, action_statement
            FROM information_schema.triggers 
            WHERE event_object_table = 'user_playlists'
            AND trigger_name = 'set_timestamp_user_playlists'
        `);

        if (triggerInfo.rowCount === 0) {
            console.log('❌ set_timestamp_user_playlists trigger does not exist');
        } else {
            console.log('✅ set_timestamp_user_playlists trigger exists');
        }

        // Check all available tables
        console.log('\n📊 All tables in database:');
        const allTables = await db.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);
        
        allTables.rows.forEach(table => {
            console.log(`  - ${table.table_name}`);
        });

        process.exit(0);
    } catch (err) {
        console.error('❌ Schema check failed:');
        console.error('Error:', err.message);
        console.error('Code:', err.code);
        process.exit(1);
    }
}

checkSchema();
