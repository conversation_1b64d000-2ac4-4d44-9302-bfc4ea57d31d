const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const db = require('./db');

async function runMigrations() {
    console.log('Running migrations in backend/migrations ...');

    if (!process.env.DATABASE_URL) {
        console.error('ERROR: DATABASE_URL is not set in backend/.env');
        process.exit(1);
    }

    try {
        const migrationsDir = path.join(__dirname, 'migrations');
        const files = fs
            .readdirSync(migrationsDir)
            .filter((f) => f.endsWith('.sql'))
            .sort();

        if (files.length === 0) {
            console.log('No SQL migration files found.');
            process.exit(0);
        }

        for (const file of files) {
            const filePath = path.join(migrationsDir, file);
            console.log(`\n→ Executing ${file} ...`);
            const sql = fs.readFileSync(filePath, 'utf8');
            await db.query(sql);
            console.log(`✓ ${file} executed.`);
        }

        console.log('\nAll migrations executed successfully.');
        process.exit(0);
    } catch (err) {
        console.error('\n✗ Migrations failed:');
        console.error('Error:', err.message);
        console.error('Code:', err.code);
        process.exit(1);
    }
}

runMigrations();

