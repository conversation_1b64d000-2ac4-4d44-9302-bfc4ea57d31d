-- Add playlist management tables
-- This migration adds support for YouTube playlist management with access control

-- Create playlists table to store YouTube playlist information
CREATE TABLE IF NOT EXISTS playlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    youtube_playlist_id TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    video_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for playlist lookups
CREATE INDEX IF NOT EXISTS idx_playlists_youtube_id ON playlists(youtube_playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlists_active ON playlists(is_active);

-- Create user_playlists table for access control (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_playlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES users(id), -- Admin who granted access
    granted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ, -- Optional expiration date
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, playlist_id)
);

-- Create indexes for user_playlists
CREATE INDEX IF NOT EXISTS idx_user_playlists_user_id ON user_playlists(user_id);
CREATE INDEX IF NOT EXISTS idx_user_playlists_playlist_id ON user_playlists(playlist_id);
CREATE INDEX IF NOT EXISTS idx_user_playlists_active ON user_playlists(is_active);

-- Add trigger for playlists updated_at
CREATE TRIGGER set_timestamp_playlists
    BEFORE UPDATE ON playlists
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Add YouTube playlist integration columns to courses table if not exists
ALTER TABLE courses ADD COLUMN IF NOT EXISTS playlist_id UUID REFERENCES playlists(id);
CREATE INDEX IF NOT EXISTS idx_courses_playlist_id ON courses(playlist_id);

-- Create view for user accessible playlists
CREATE OR REPLACE VIEW user_accessible_playlists AS
SELECT 
    up.user_id,
    p.id as playlist_id,
    p.youtube_playlist_id,
    p.title,
    p.description,
    p.thumbnail_url,
    p.video_count,
    up.granted_at,
    up.expires_at,
    up.is_active as access_active,
    p.is_active as playlist_active,
    CASE 
        WHEN up.expires_at IS NULL OR up.expires_at > NOW() THEN true
        ELSE false
    END as access_valid
FROM user_playlists up
JOIN playlists p ON up.playlist_id = p.id
WHERE up.is_active = true 
  AND p.is_active = true;

-- Create view for admin playlist management
CREATE OR REPLACE VIEW admin_playlist_overview AS
SELECT 
    p.id as playlist_id,
    p.youtube_playlist_id,
    p.title,
    p.description,
    p.video_count,
    p.is_active,
    p.created_at,
    p.updated_at,
    COUNT(up.user_id) as total_users_with_access,
    COUNT(CASE WHEN up.is_active = true AND (up.expires_at IS NULL OR up.expires_at > NOW()) THEN 1 END) as active_users_with_access
FROM playlists p
LEFT JOIN user_playlists up ON p.id = up.playlist_id
GROUP BY p.id, p.youtube_playlist_id, p.title, p.description, p.video_count, p.is_active, p.created_at, p.updated_at
ORDER BY p.created_at DESC;
