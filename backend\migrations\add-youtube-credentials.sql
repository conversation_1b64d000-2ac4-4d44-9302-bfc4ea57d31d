-- Add YouTube API credentials fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_channel_id TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_api_key TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_client_id TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_client_secret TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_refresh_token TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_access_token TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS youtube_token_expiry TIMESTAMPTZ;

-- Add indexes for YouTube-related queries
CREATE INDEX IF NOT EXISTS idx_users_youtube_channel_id ON users(youtube_channel_id);

-- Update courses table to link with YouTube playlists
ALTER TABLE courses ADD COLUMN IF NOT EXISTS youtube_playlist_id TEXT UNIQUE;
ALTER TABLE courses ADD COLUMN IF NOT EXISTS youtube_playlist_url TEXT;
ALTER TABLE courses ADD COLUMN IF NOT EXISTS youtube_channel_title TEXT;
ALTER TABLE courses ADD COLUMN IF NOT EXISTS youtube_video_count INTEGER DEFAULT 0;
ALTER TABLE courses ADD COLUMN IF NOT EXISTS youtube_thumbnail_url TEXT;
ALTER TABLE courses ADD COLUMN IF NOT EXISTS synced_at TIMESTAMPTZ;

-- Add index for YouTube playlist lookups
CREATE INDEX IF NOT EXISTS idx_courses_youtube_playlist_id ON courses(youtube_playlist_id);
