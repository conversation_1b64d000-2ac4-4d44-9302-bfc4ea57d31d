/**
 * YouTube Data API v3 Service
 * Handles fetching playlist and video data from YouTube API
 */

const API_KEY = process.env.REACT_APP_YOUTUBE_API_KEY || "AIzaSyAYX0o6fdMGCp7WxzEsrSzJ0dNxS9orJzM";
const BASE_URL = "https://www.googleapis.com/youtube/v3";

class YouTubeService {
  /**
   * Fetch playlist details by playlist ID
   * @param {string} playlistId - YouTube playlist ID
   * @returns {Promise<Object>} Playlist data
   */
  async fetchPlaylistDetails(playlistId) {
    try {
      const url = `${BASE_URL}/playlists?part=snippet,contentDetails&id=${playlistId}&key=${API_KEY}`;
      const response = await fetch(url);
      const data = await response.json();

      if (data.error) {
        throw new Error(`YouTube API Error: ${data.error.message} (Code: ${data.error.code})`);
      }

      if (!data.items || data.items.length === 0) {
        throw new Error('Playlist not found or not accessible. Please check the playlist ID and make sure it\'s public.');
      }

      return data.items[0];
    } catch (error) {
      console.error('Error fetching playlist details:', error);
      throw error;
    }
  }

  /**
   * Fetch videos from a playlist
   * @param {string} playlistId - YouTube playlist ID
   * @param {number} maxResults - Maximum number of videos to fetch (default: 50)
   * @returns {Promise<Array>} Array of video data
   */
  async fetchPlaylistVideos(playlistId, maxResults = 50) {
    try {
      const url = `${BASE_URL}/playlistItems?part=snippet,contentDetails&playlistId=${playlistId}&maxResults=${maxResults}&key=${API_KEY}`;
      const response = await fetch(url);
      const data = await response.json();

      if (data.error) {
        throw new Error(`YouTube API Error: ${data.error.message} (Code: ${data.error.code})`);
      }

      if (!data.items || data.items.length === 0) {
        throw new Error('No videos found in this playlist');
      }

      return data.items;
    } catch (error) {
      console.error('Error fetching playlist videos:', error);
      throw error;
    }
  }

  /**
   * Fetch both playlist details and videos in a single call
   * @param {string} playlistId - YouTube playlist ID
   * @param {number} maxResults - Maximum number of videos to fetch (default: 50)
   * @returns {Promise<Object>} Object containing playlist details and videos
   */
  async fetchPlaylistData(playlistId, maxResults = 50) {
    try {
      const [playlistDetails, videos] = await Promise.all([
        this.fetchPlaylistDetails(playlistId),
        this.fetchPlaylistVideos(playlistId, maxResults)
      ]);

      return {
        playlist: playlistDetails,
        videos: videos
      };
    } catch (error) {
      console.error('Error fetching playlist data:', error);
      throw error;
    }
  }

  /**
   * Transform YouTube playlist data to match the application's course structure
   * @param {Object} playlistData - Raw YouTube playlist data
   * @returns {Object} Transformed course data
   */
  transformPlaylistToCourse(playlistData) {
    const { playlist, videos } = playlistData;
    
    return {
      id: playlist.id,
      name: playlist.snippet.title,
      description: playlist.snippet.description,
      thumbnail: playlist.snippet.thumbnails?.high?.url || 
                playlist.snippet.thumbnails?.medium?.url || 
                playlist.snippet.thumbnails?.default?.url,
      videoCount: playlist.contentDetails?.itemCount || videos.length,
      metadata: `YouTube Playlist • ${videos.length} videos`,
      channelTitle: playlist.snippet.channelTitle,
      youtube_playlist_id: playlist.id,
      youtube_channel_title: playlist.snippet.channelTitle,
      publishedAt: playlist.snippet.publishedAt
    };
  }

  /**
   * Get multiple playlists data (for dashboard)
   * @param {Array<string>} playlistIds - Array of YouTube playlist IDs
   * @returns {Promise<Array>} Array of transformed course data
   */
  async fetchMultiplePlaylists(playlistIds) {
    try {
      const playlistPromises = playlistIds.map(id => this.fetchPlaylistData(id));
      const playlistsData = await Promise.all(playlistPromises);
      
      return playlistsData.map(data => this.transformPlaylistToCourse(data));
    } catch (error) {
      console.error('Error fetching multiple playlists:', error);
      throw error;
    }
  }

  /**
   * Search for playlists by query
   * @param {string} query - Search query
   * @param {number} maxResults - Maximum number of results (default: 10)
   * @returns {Promise<Array>} Array of playlist search results
   */
  async searchPlaylists(query, maxResults = 10) {
    try {
      const url = `${BASE_URL}/search?part=snippet&type=playlist&q=${encodeURIComponent(query)}&maxResults=${maxResults}&key=${API_KEY}`;
      const response = await fetch(url);
      const data = await response.json();

      if (data.error) {
        throw new Error(`YouTube API Error: ${data.error.message} (Code: ${data.error.code})`);
      }

      if (!data.items || data.items.length === 0) {
        return [];
      }

      // Transform search results to course format
      return data.items.map(item => ({
        id: item.id.playlistId,
        name: item.snippet.title,
        description: item.snippet.description,
        thumbnail: item.snippet.thumbnails?.high?.url || 
                  item.snippet.thumbnails?.medium?.url || 
                  item.snippet.thumbnails?.default?.url,
        metadata: `YouTube Playlist • ${item.snippet.channelTitle}`,
        channelTitle: item.snippet.channelTitle,
        youtube_playlist_id: item.id.playlistId,
        youtube_channel_title: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt
      }));
    } catch (error) {
      console.error('Error searching playlists:', error);
      throw error;
    }
  }

  /**
   * Check if YouTube API key is configured
   * @returns {boolean} True if API key is available
   */
  isConfigured() {
    return !!API_KEY && API_KEY !== 'your-api-key-here';
  }

  /**
   * Get API configuration status
   * @returns {Object} Configuration status information
   */
  getConfigStatus() {
    return {
      hasApiKey: !!API_KEY,
      isConfigured: this.isConfigured(),
      apiKey: API_KEY ? `${API_KEY.substring(0, 8)}...` : 'Not set'
    };
  }
}

// Export a singleton instance
export default new YouTubeService();
