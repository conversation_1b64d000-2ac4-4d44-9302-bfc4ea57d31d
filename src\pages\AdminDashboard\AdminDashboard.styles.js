import styled from 'styled-components';

export const AdminDashboardContainer = styled.div`
  height: 100dvh;
  max-width: var(--max-width);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
`;

export const Main = styled.main`
  height: calc(100dvh - var(--header-height) - var(--footer-height));
  width: 100%;
  margin-top: var(--header-height);
  display: flex;
  flex-direction: column;
`;

export const MainHeader = styled.div`
  width: 100%;
  height: 50px;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: var(--border-width) var(--border-style) var(--border-color);
  margin-bottom: var(--spacing-lg);
`;

export const MainTitle = styled.h1`
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  user-select: none;
`;

export const MainContent = styled.div`
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  scrollbar-width: none;
  pointer-events: auto;
  data-scrollable: true;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-hover);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary);
  }
`;

export const StudentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

export const StudentItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border: var(--border-width) var(--border-style) var(--border-color);
  border-radius: 12px;
  background-color: var(--color-background);
  transition: border-color 0.2s ease;

  &:hover {
    border-color: var(--color-primary);
  }
`;

export const StudentInfo = styled.div`
  flex: 1;
`;

export const StudentName = styled.h3`
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
`;

export const StudentEmail = styled.p`
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--color-text);
  opacity: 0.8;
`;

export const StudentActions = styled.div`
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
`;

export const ActionButton = styled.button.withConfig({
  shouldForwardProp: (prop) => !['danger'].includes(prop),
})`
  padding: var(--spacing-sm) var(--spacing-md);
  border: var(--border-width) var(--border-style) var(--color-border);
  border-radius: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background-color: var(--color-hover);
    border-color: var(--color-primary);
    color: var(--color-primary);
  }

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  ${({ danger }) => danger && `
    &:hover {
      background-color: #ffe6e6;
      border-color: #ff4444;
      color: #ff4444;
    }
  `}
`;

export const AddStudentButton = styled.button`
  padding: var(--spacing-sm) var(--spacing-lg);
  border: var(--border-width) var(--border-style) var(--color-primary);
  border-radius: 8px;
  background-color: var(--color-primary);
  color: var(--color-background);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }
`;

// Modal Styles
export const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

export const ModalContent = styled.div`
  background-color: var(--color-background);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
`;

export const ModalHeader = styled.div`
  padding: var(--spacing-lg);
  border-bottom: var(--border-width) var(--border-style) var(--border-color);
`;

export const ModalTitle = styled.h2`
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
`;

export const ModalBody = styled.div`
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
`;

export const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
`;

export const Label = styled.label`
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  user-select: none;
`;

export const Input = styled.input`
  padding: var(--spacing-md);
  border: var(--border-width) var(--border-style) var(--color-border);
  border-radius: 8px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-text);
  background-color: var(--color-background);
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  &::placeholder {
    color: var(--color-text);
    opacity: 0.6;
  }
`;

export const Textarea = styled.textarea`
  padding: var(--spacing-md);
  border: var(--border-width) var(--border-style) var(--color-border);
  border-radius: 8px;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-text);
  background-color: var(--color-background);
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 120px;

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  &::placeholder {
    color: var(--color-text);
    opacity: 0.6;
  }
`;

export const ModalFooter = styled.div`
  padding: var(--spacing-lg);
  border-top: var(--border-width) var(--border-style) var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
`;

export const CancelButton = styled.button`
  padding: var(--spacing-sm) var(--spacing-lg);
  border: var(--border-width) var(--border-style) var(--color-border);
  border-radius: 8px;
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background-color: var(--color-hover);
  }

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }
`;

export const SubmitButton = styled.button`
  padding: var(--spacing-sm) var(--spacing-lg);
  border: var(--border-width) var(--border-style) var(--color-primary);
  border-radius: 8px;
  background-color: var(--color-primary);
  color: var(--color-background);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;

  &:hover {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }
`;

// Text content components
export const LoadingText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;

export const ErrorText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;

export const EmptyText = styled.div`
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  text-align: center;
  padding: var(--spacing-2xl);
  user-select: none;
`;
