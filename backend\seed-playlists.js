const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const db = require('./db');

// Playlist data extracted from links.md
const playlistsData = [
    {
        youtube_playlist_id: 'PLIddAt-uiBBUReBRX2tnTabs35sgES-eJ',
        title: 'LABORATORIO LA TÉCNICA DEL GRECO (2025)',
        description: 'Aprende las técnicas pictóricas del maestro El Greco en este laboratorio práctico.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVCRGoC9b8FdjFi_q_kuDxu',
        title: 'LABORATORIO TEORÍA Y PRÁCTICA DEL COLOR (2025)',
        description: 'Domina la teoría del color y su aplicación práctica en la pintura.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXBNz6P0IoL6COrEXanjyWh',
        title: 'LABORATORIO DE COMPOSICIÓN PICTÓRICA',
        description: 'Aprende los fundamentos de la composición en la pintura.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBUxInNAxBnMyMIQdTXjLi_4',
        title: 'LABORATORIO LA TÉCNICA DE SOROLLA 2025',
        description: 'Explora las técnicas luminosas del maestro Joaquín Sorolla.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBUa7-DylX6_Pj1eCblCReCY',
        title: 'LABORATORIO DIBUJO DE RETRATOS',
        description: 'Perfecciona tus habilidades en el dibujo de retratos.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVaTRHopN0ItPmqAqCWxeRe',
        title: 'LABORATORIO LA TÉCNICA DE LUCIAN FREUD',
        description: 'Descubre las técnicas del pintor británico Lucian Freud.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXSgO1bUvRCZe1hEaT7_zp_',
        title: 'LABORATORIO LA TÉCNICA DE CARAVAGGIO 2025',
        description: 'Aprende el claroscuro y las técnicas del maestro Caravaggio.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXVlXKzW1WyZ3Urc5lFMCCf',
        title: 'LABORATORIO LA TÉCNICA DE TURNER',
        description: 'Explora las técnicas atmosféricas de J.M.W. Turner.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBUFgJur6Z5OTfcsmS8oWH4N',
        title: 'INTRODUCCIÓN AL DIBUJO',
        description: 'Curso básico de introducción a las técnicas de dibujo.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVzVqyLdDJwdduljoNTNlVC',
        title: 'LABORATORIO TEORÍA DEL COLOR',
        description: 'Fundamentos teóricos del color en la pintura.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXRKASp--hd7313Ebj2O4W5',
        title: 'LABORATORIO TÉCNICAS DE ENCÁUSTICA',
        description: 'Aprende la antigua técnica de la pintura encáustica.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBX7bU2f_TtjemPvyoqojXHj',
        title: 'LABORATORIO LA TÉCNICA DE DEGAS',
        description: 'Descubre las técnicas del maestro Edgar Degas.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBW2DNXgm8i9lU1jdyUuFAcg',
        title: 'LABORATORIO LA TÉCNICA DE RUBENS',
        description: 'Explora las técnicas barrocas de Peter Paul Rubens.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXXt5onUsvGa3fzX_tEm2L-',
        title: 'LABORATORIO LA TÉCNICA DE SOROLLA',
        description: 'Técnicas luminosas del maestro valenciano Joaquín Sorolla.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVwJvvYLDqsyNhpuDcmpqCv',
        title: 'LABORATORIO LA TÉCNICA DE BOTTICELLI',
        description: 'Aprende las técnicas renacentistas de Sandro Botticelli.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVI1Evz6FgU4SaqFqXWpYs7',
        title: 'LABORATORIO PREPARACIÓN DE SOPORTES',
        description: 'Aprende a preparar correctamente los soportes para pintura.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBX9-EDCpgxl1k37UyBEXklK',
        title: 'LA TÉCNICA DE CARAVAGGIO',
        description: 'Técnicas del maestro del claroscuro Michelangelo Merisi da Caravaggio.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBV4Dby3JC9oI6cgFBTYel2o',
        title: 'LABORATORIO LA TÉCNICA DE REMBRANDT',
        description: 'Descubre las técnicas del maestro holandés Rembrandt van Rijn.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBX700CUqmYoW3h6aOKiBc3h',
        title: 'LABORATORIO LA TÉCNICA DE VAN GOGH',
        description: 'Explora las técnicas expresivas de Vincent van Gogh.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBW1CbVmRz4_tDksy-dK1ySQ',
        title: 'LABORATORIO ALQUIMIA DEL COLOR',
        description: 'Profundiza en los secretos y la alquimia del color.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBWlkUrMjujshnPMvUf5sq-L',
        title: 'LABORATORIO DIBUJO DE LA FIGURA HUMANA I',
        description: 'Primer nivel del dibujo de la figura humana.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBUg82sTOp69f9HykOJ0MIdY',
        title: 'LABORATORIO LA TÉCNICA DE VELÁZQUEZ',
        description: 'Aprende las técnicas del maestro Diego Velázquez.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBXP1iPC5BR6npMEW7JPZRgy',
        title: 'Laboratorio la Técnica de Monet',
        description: 'Descubre las técnicas impresionistas de Claude Monet.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBVZFy3CPT3yR_0pigBHnlNJ',
        title: 'Laboratorio La Técnica de Vermeer',
        description: 'Explora las técnicas del maestro Johannes Vermeer.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBUhI4t1XR1dx8VoJh8LW4W4',
        title: 'Laboratorio la técnica de el Greco',
        description: 'Técnicas del maestro cretense Doménikos Theotokópoulos.'
    },
    {
        youtube_playlist_id: 'PLIddAt-uiBBWthEGVvUC2d2-BNdzfjoQg',
        title: 'Laboratorio La Técnica de da Vinci',
        description: 'Aprende las técnicas del genio renacentista Leonardo da Vinci.'
    }
];

async function seedPlaylists() {
    try {
        console.log('Starting playlist seeding...');
        
        // Insert playlists
        for (const playlist of playlistsData) {
            try {
                const result = await db.query(
                    `INSERT INTO playlists (youtube_playlist_id, title, description)
                     VALUES ($1, $2, $3)
                     ON CONFLICT (youtube_playlist_id) 
                     DO UPDATE SET 
                        title = EXCLUDED.title,
                        description = EXCLUDED.description,
                        updated_at = NOW()
                     RETURNING id, title`,
                    [playlist.youtube_playlist_id, playlist.title, playlist.description]
                );
                
                console.log(`✓ Seeded playlist: ${result.rows[0].title}`);
            } catch (err) {
                console.error(`✗ Error seeding playlist ${playlist.title}:`, err.message);
            }
        }
        
        console.log(`\nPlaylist seeding completed! Total playlists: ${playlistsData.length}`);
        
        // Show summary
        const countResult = await db.query('SELECT COUNT(*) as total FROM playlists');
        console.log(`Total playlists in database: ${countResult.rows[0].total}`);
        
    } catch (err) {
        console.error('Playlist seeding failed:', err);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    seedPlaylists()
        .then(() => {
            console.log('Seeding completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('Seeding failed:', err);
            process.exit(1);
        });
}

module.exports = { seedPlaylists, playlistsData };
