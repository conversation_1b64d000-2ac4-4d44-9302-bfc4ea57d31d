import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import 'boxicons/css/boxicons.min.css';
import VideoPlayHeader from '../../components/Layout/VideoPlayHeader';
import VideoControls from '../../components/VideoControls/VideoControls';
import api from '../../services/api';
import {
  VideoPlayContainer,
  VideoPlayContent,
  MainContent,
  LeftColumn,
  RightColumn,
  PrimaryVideoSection,
  VideoPlayer,
  VideoPlayerIframe,
  MainVideo,
  VideoInfo,
  VideoTitle,
  VideoMetadata,
  ChannelInfo,
  ChannelAvatar,
  ChannelDetails,
  ChannelName,
  Subscribers,
  VideoPlaySidebar,
  SidebarItem,
  ThumbnailContainer,
  SidebarVideo,
  VideoDuration,
  VideoDetails,
  VideoTitleSidebar,
  ChannelNameSidebar,
  VideoStats,
  SuggestionOverlay
} from './VideoPlay.styles';

const VideoPlay = () => {
  const { playlistId } = useParams();
  const [playlist, setPlaylist] = useState(null);
  const [videos, setVideos] = useState([]);
  const [currentVideoId, setCurrentVideoId] = useState(null);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // YouTube player state
  const playerRef = useRef(null);
  const videoPlayerRef = useRef(null); // Ref for fullscreen container
  const [player, setPlayer] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasPlayed, setHasPlayed] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(100);
  const [showControls, setShowControls] = useState(false);
  const [controlsTimeout, setControlsTimeout] = useState(null);
  const [isHoveringControls, setIsHoveringControls] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (playlistId) {
      loadPlaylistAndVideos();
    }
  }, [playlistId]);

  // Handle fullscreen changes (including F11, Escape, etc.)
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);


  // Initialize YouTube iframe API
  useEffect(() => {
    // Load YouTube iframe API if not already loaded
    if (!window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
      
      window.onYouTubeIframeAPIReady = () => {
        if (currentVideoId) {
          initializePlayer();
        }
      };
    } else if (window.YT && window.YT.Player && currentVideoId) {
      initializePlayer();
    }

    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
      if (playerRef.current) {
        clearInterval(playerRef.current);
      }
      if (player && typeof player.destroy === 'function') {
        player.destroy();
      }
    };
  }, [currentVideoId]);

  const initializePlayer = () => {
    console.log('initializePlayer called', { currentVideoId, hasYT: !!window.YT, hasPlayer: !!(window.YT && window.YT.Player) });
    if (currentVideoId && window.YT && window.YT.Player) {
      // Destroy existing player if it exists
      if (player && typeof player.destroy === 'function') {
        console.log('Destroying existing player');
        player.destroy();
      }

      console.log('Creating new YouTube player');
      const newPlayer = new window.YT.Player('youtube-player', {
        height: '100%',
        width: '100%',
        videoId: currentVideoId,
        playerVars: {
          autoplay: 0,
          controls: 0,
          disablekb: 1,
          fs: 0,
          iv_load_policy: 3,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          cc_load_policy: 0,
          playsinline: 1,
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
        },
      });
      setPlayer(newPlayer);
      console.log('Player created', newPlayer);
    }
  };

  const onPlayerReady = (event) => {
    const playerInstance = event.target;
    setDuration(playerInstance.getDuration());
    setVolume(playerInstance.getVolume());
    
    // Start time tracking
    if (playerRef.current) {
      clearInterval(playerRef.current);
    }
    
    playerRef.current = setInterval(() => {
      if (playerInstance && typeof playerInstance.getCurrentTime === 'function') {
        setCurrentTime(playerInstance.getCurrentTime());
      }
    }, 1000);
  };

  const onPlayerStateChange = (event) => {
    const playerState = event.data;
    const wasPlaying = playerState === window.YT.PlayerState.PLAYING;
    setIsPlaying(wasPlaying);

    // Set hasPlayed to true when video starts playing for the first time
    if (wasPlaying && !hasPlayed) {
      setHasPlayed(true);
    }
  };

  // Control functions
  const handlePlayPause = () => {
    console.log('handlePlayPause called', { player, isPlaying, hasPlayVideo: player && typeof player.playVideo === 'function' });
    if (player && typeof player.playVideo === 'function' && typeof player.pauseVideo === 'function') {
      if (isPlaying) {
        player.pauseVideo();
      } else {
        player.playVideo();
      }
    } else {
      console.error('Player not ready or missing methods', player);
    }
  };

  const handleSeek = (time) => {
    if (player) {
      player.seekTo(time, true);
      setCurrentTime(time);
    }
  };

  const handleVolumeChange = (newVolume) => {
    if (player) {
      player.setVolume(newVolume);
      setVolume(newVolume);
    }
  };

  const handleFullscreen = () => {
    if (!videoPlayerRef.current) return;

    if (!isFullscreen) {
      // Enter fullscreen
      const elem = videoPlayerRef.current;
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.webkitRequestFullscreen) { // Safari
        elem.webkitRequestFullscreen();
      } else if (elem.mozRequestFullScreen) { // Firefox
        elem.mozRequestFullScreen();
      } else if (elem.msRequestFullscreen) { // IE11
        elem.msRequestFullscreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) { // Safari
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) { // Firefox
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) { // IE11
        document.msExitFullscreen();
      }
    }
  };

  // Show/hide controls
  const handleMouseMove = () => {
    setShowControls(true);

    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }

    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 2000);

    setControlsTimeout(timeout);
  };

  const handleMouseLeave = () => {
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    // Don't hide controls if hovering over controls area
    if (!isHoveringControls) {
      setShowControls(false);
    }
  };

  const handleControlsHover = () => {
    setIsHoveringControls(true);
    setShowControls(true);
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
  };

  const handleControlsLeave = () => {
    setIsHoveringControls(false);
    // Start timeout to hide controls after leaving
    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 2000);
    setControlsTimeout(timeout);
  };

  const loadPlaylistAndVideos = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load playlist details and videos using the new API endpoints
      const [playlistResponse, videosResponse] = await Promise.all([
        api.get(`/api/my/playlists/${playlistId}`),
        api.get(`/api/my/playlists/${playlistId}/videos`)
      ]);

      setPlaylist(playlistResponse.data);

      // Sort videos chronologically by position (oldest to newest)
      const sortedVideos = videosResponse.data.sort((a, b) => {
        const positionA = a.snippet?.position ?? Number.MAX_SAFE_INTEGER;
        const positionB = b.snippet?.position ?? Number.MAX_SAFE_INTEGER;
        return positionA - positionB;
      });

      setVideos(sortedVideos);

      // Set first video as current (after sorting)
      if (sortedVideos.length > 0) {
        const firstVideo = sortedVideos[0];
        const videoId = firstVideo.snippet?.resourceId?.videoId || firstVideo.contentDetails?.videoId || firstVideo.id;
        setCurrentVideoId(videoId);
        setCurrentVideo(firstVideo);
      }

      console.log('Successfully loaded playlist data');
    } catch (err) {
      console.error('Error loading playlist and videos:', err);
      if (err.response?.status === 403) {
        setError('No tienes acceso a esta playlist');
      } else if (err.response?.status === 404) {
        setError('Playlist no encontrada');
      } else {
        setError(err.message || 'Error cargando la playlist y videos');
      }
    } finally {
      setLoading(false);
    }
  };

  const selectVideo = (video) => {
    const newVideoId = video.snippet?.resourceId?.videoId || video.contentDetails?.videoId || video.id;
    setCurrentVideoId(newVideoId);
    setCurrentVideo(video);

    // Reset player state
    setIsPlaying(false);
    setHasPlayed(false); // Reset hasPlayed for new video
    setCurrentTime(0);
    setDuration(0);

    // Load new video in existing player
    if (player && newVideoId && typeof player.loadVideoById === 'function') {
      player.loadVideoById(newVideoId);
    }
  };

  const formatDuration = (duration) => {
    // Parse ISO 8601 duration (PT12M45S)
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return '0:00';

    const hours = parseInt(match[1] || 0);
    const minutes = parseInt(match[2] || 0);
    const seconds = parseInt(match[3] || 0);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatPublishedDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  if (loading) {
    return (
      <VideoPlayContainer>
        <VideoPlayHeader />
        <VideoPlayContent>
          <div style={{ textAlign: 'center', padding: '2rem' }}>Cargando playlist...</div>
        </VideoPlayContent>
      </VideoPlayContainer>
    );
  }

  if (error) {
    return (
      <VideoPlayContainer>
        <VideoPlayHeader />
        <VideoPlayContent>
          <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>{error}</div>
        </VideoPlayContent>
      </VideoPlayContainer>
    );
  }

  return (
    <VideoPlayContainer>
      {!isFullscreen && <VideoPlayHeader />}
      <VideoPlayContent>
        <MainContent $isFullscreen={isFullscreen}>
          {/* Left Column - Just Video */}
          <LeftColumn>
            <PrimaryVideoSection>
              <VideoPlayer ref={videoPlayerRef} $isFullscreen={isFullscreen}>
                {/* Invisible overlay to capture mouse events */}
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 5,
                    pointerEvents: 'auto',
                    background: 'transparent',
                    cursor: 'pointer'
                  }}
                  onMouseMove={handleMouseMove}
                  onMouseLeave={handleMouseLeave}
                  onClick={handlePlayPause}
                />
                {currentVideoId ? (
                  <>
                    <div
                      id="youtube-player"
                      style={{
                        width: '100%',
                        height: '100%',
                        pointerEvents: 'none'
                      }}
                    />
                    {!hasPlayed && currentVideoId && (
                      <SuggestionOverlay>
                        <div style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: '24px',
                          textAlign: 'center',
                          padding: '20px'
                        }}>
                          <div style={{
                            fontSize: '1.8rem',
                            fontWeight: '600',
                            color: 'white',
                            textShadow: '0 2px 4px rgba(0,0,0,0.5)',
                            maxWidth: '80%',
                            lineHeight: '1.3'
                          }}>
                            {currentVideo?.snippet?.title || "Video"}
                          </div>
                          <button
                            onClick={handlePlayPause}
                            onMouseEnter={(e) => {
                              // Prevent controls from showing when hovering the button
                              e.stopPropagation();
                              e.target.style.transform = 'scale(1.05)';
                              e.target.style.boxShadow = '0 6px 16px rgba(0,0,0,0.4)';
                            }}
                            onMouseLeave={(e) => {
                              // Prevent controls from showing when leaving the button
                              e.stopPropagation();
                              e.target.style.transform = 'scale(1)';
                              e.target.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
                            }}
                            onMouseMove={(e) => {
                              // Prevent mouse move events from triggering controls
                              e.stopPropagation();
                            }}
                            style={{
                              backgroundColor: 'var(--color-primary, #ff6b35)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '50px',
                              padding: '16px 32px',
                              fontSize: '1.1rem',
                              fontWeight: '600',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              transition: 'all 0.2s ease',
                              boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
                            }}
                          >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M8 5v14l11-7z"/>
                            </svg>
                            REPRODUCIR
                          </button>
                        </div>
                      </SuggestionOverlay>
                    )}

                    {/* Blur overlay when controls are shown or video is paused */}
                    {hasPlayed && (showControls || !isPlaying) && (
                      <div
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.3)',
                          backdropFilter: 'blur(1px)',
                          zIndex: 6,
                          pointerEvents: 'none'
                        }}
                      />
                    )}

                    {/* Center play/pause icon when controls are shown or video is paused */}
                    {hasPlayed && (showControls || !isPlaying) && (
                      <div
                        style={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          zIndex: 8,
                          pointerEvents: 'none'
                        }}
                      >
                        <div
                          style={{
                            color: 'white',
                            width: '60px',
                            height: '60px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            userSelect: 'none'
                          }}
                        >
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            {isPlaying ? (
                              <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                            ) : (
                              <path d="M8 5v14l11-7z"/>
                            )}
                          </svg>
                        </div>
                      </div>
                    )}

                    <VideoControls
                      player={player}
                      isPlaying={isPlaying}
                      onPlayPause={handlePlayPause}
                      currentTime={currentTime}
                      duration={duration}
                      onSeek={handleSeek}
                      volume={volume}
                      onVolumeChange={handleVolumeChange}
                      onFullscreen={handleFullscreen}
                      showControls={hasPlayed && (showControls || !isPlaying)}
                      onControlsHover={handleControlsHover}
                      onControlsLeave={handleControlsLeave}
                    />
                  </>
                ) : (
                  <MainVideo controls>
                    <source src="" type="video/mp4" />
                    Your browser does not support the video tag.
                  </MainVideo>
                )}
              </VideoPlayer>
            </PrimaryVideoSection>
          </LeftColumn>

          {/* Right Column - Video Info and Sidebar */}
          {!isFullscreen && (
            <RightColumn>
            {/* Video Info */}
            <VideoInfo>
              <VideoTitle>{currentVideo?.snippet?.title || "Selecciona un video"}</VideoTitle>
              <VideoMetadata>
                <span className="date">
                  {currentVideo?.snippet?.publishedAt ? formatPublishedDate(currentVideo.snippet.publishedAt) : ""}
                </span>
              </VideoMetadata>
              <ChannelInfo>
                <ChannelAvatar />
                <ChannelDetails>
                  <ChannelName>Zona Cero Academy</ChannelName>
                  <Subscribers>Playlist privada</Subscribers>
                </ChannelDetails>
              </ChannelInfo>
            </VideoInfo>

            {/* Sidebar with Playlist Videos */}
            <VideoPlaySidebar>
              {videos.map((video, index) => (
                <SidebarItem
                  key={video.id || index}
                  onClick={() => selectVideo(video)}
                  style={{
                    cursor: 'pointer',
                    backgroundColor: currentVideoId === (video.snippet?.resourceId?.videoId || video.contentDetails?.videoId || video.id) ? 'var(--color-background-secondary)' : 'transparent'
                  }}
                >
                  <ThumbnailContainer>
                    <img
                      src={
                        video.snippet?.thumbnails?.medium?.url ||
                        video.snippet?.thumbnails?.high?.url ||
                        video.snippet?.thumbnails?.default?.url ||
                        video.snippet?.thumbnails?.standard?.url ||
                        video.snippet?.thumbnails?.maxres?.url ||
                        "https://via.placeholder.com/120x90?text=No+Thumbnail"
                      }
                      alt={video.snippet?.title || "Video thumbnail"}
                      onError={(e) => {
                        // Try fallback thumbnails in order of preference
                        const thumbnails = video.snippet?.thumbnails;
                        const fallbackUrls = [
                          thumbnails?.high?.url,
                          thumbnails?.default?.url,
                          thumbnails?.standard?.url,
                          thumbnails?.maxres?.url,
                          "https://via.placeholder.com/120x90?text=Error+Loading"
                        ].filter(Boolean);

                        const currentSrc = e.target.src;
                        const currentIndex = fallbackUrls.findIndex(url => url === currentSrc);
                        const nextUrl = fallbackUrls[currentIndex + 1];

                        if (nextUrl && nextUrl !== currentSrc) {
                          e.target.src = nextUrl;
                        }
                      }}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: '4px',
                        display: 'block'
                      }}
                    />
                    <VideoDuration>
                      {video.contentDetails?.duration ? formatDuration(video.contentDetails.duration) : "0:00"}
                    </VideoDuration>
                  </ThumbnailContainer>
                  <VideoDetails>
                    <VideoTitleSidebar>{video.snippet?.title}</VideoTitleSidebar>
                    <ChannelNameSidebar>Zona Cero Academy</ChannelNameSidebar>
                    <VideoStats>
                      <span>{video.snippet?.publishedAt ? formatPublishedDate(video.snippet.publishedAt) : ""}</span>
                    </VideoStats>
                  </VideoDetails>
                </SidebarItem>
              ))}
            </VideoPlaySidebar>
          </RightColumn>
          )}
        </MainContent>
      </VideoPlayContent>
    </VideoPlayContainer>
  );
};

export default VideoPlay;
