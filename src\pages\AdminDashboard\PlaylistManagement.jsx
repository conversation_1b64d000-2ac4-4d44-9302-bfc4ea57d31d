import React, { useState, useEffect } from 'react';
import api from '../../services/api';
import {
  LoadingText,
  ErrorText,
  EmptyText,
  ActionButton,
  Modal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalBody,
  FormGroup,
  Label,
  Input,
  ModalFooter,
  CancelButton,
  SubmitButton
} from './AdminDashboard.styles';

const PlaylistManagement = () => {
  const [playlists, setPlaylists] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [showAccessModal, setShowAccessModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [playlistUsers, setPlaylistUsers] = useState([]);
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    isActive: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [playlistsResponse, usersResponse] = await Promise.all([
        api.get('/api/admin/playlists'),
        api.get('/api/admin/users')
      ]);
      
      setPlaylists(playlistsResponse.data);
      // Filter out admin users
      setUsers(usersResponse.data.filter(user => user.role !== 'admin'));
      setError(null);
    } catch (err) {
      console.error('Error loading playlist data:', err);
      setError('Error cargando datos de playlists');
    } finally {
      setLoading(false);
    }
  };

  const loadPlaylistDetails = async (playlistId) => {
    try {
      const response = await api.get(`/api/admin/playlists/${playlistId}`);
      setSelectedPlaylist(response.data.playlist);
      setPlaylistUsers(response.data.users);
      setEditForm({
        title: response.data.playlist.title,
        description: response.data.playlist.description || '',
        isActive: response.data.playlist.is_active
      });
    } catch (err) {
      console.error('Error loading playlist details:', err);
      setError('Error cargando detalles de la playlist');
    }
  };

  const handleManageAccess = async (playlist) => {
    await loadPlaylistDetails(playlist.playlist_id);
    setShowAccessModal(true);
  };

  const handleEditPlaylist = async (playlist) => {
    await loadPlaylistDetails(playlist.playlist_id);
    setShowEditModal(true);
  };

  const handleGrantAccess = async (userId) => {
    if (!selectedPlaylist) return;
    
    try {
      await api.post(`/api/admin/playlists/${selectedPlaylist.id}/users/${userId}`);
      await loadPlaylistDetails(selectedPlaylist.id);
      await loadData(); // Refresh main list
    } catch (err) {
      console.error('Error granting access:', err);
      setError('Error otorgando acceso');
    }
  };

  const handleRevokeAccess = async (userId) => {
    if (!selectedPlaylist) return;
    
    try {
      await api.delete(`/api/admin/playlists/${selectedPlaylist.id}/users/${userId}`);
      await loadPlaylistDetails(selectedPlaylist.id);
      await loadData(); // Refresh main list
    } catch (err) {
      console.error('Error revoking access:', err);
      setError('Error revocando acceso');
    }
  };

  const handleUpdatePlaylist = async (e) => {
    e.preventDefault();
    if (!selectedPlaylist) return;

    try {
      await api.put(`/api/admin/playlists/${selectedPlaylist.id}`, editForm);
      setShowEditModal(false);
      await loadData(); // Refresh main list
    } catch (err) {
      console.error('Error updating playlist:', err);
      setError('Error actualizando playlist');
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  if (loading) return <LoadingText>Cargando playlists...</LoadingText>;
  if (error) return <ErrorText>Error: {error}</ErrorText>;

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h3>Gestión de Playlists de YouTube</h3>
        <div style={{ fontSize: 'var(--font-size-sm)', color: 'var(--color-text-secondary)' }}>
          Total: {playlists.length} playlists
        </div>
      </div>

      {playlists.length === 0 ? (
        <EmptyText>No hay playlists disponibles</EmptyText>
      ) : (
        <div style={{ display: 'grid', gap: '1rem' }}>
          {playlists.map((playlist) => (
            <div
              key={playlist.playlist_id}
              style={{
                padding: '1.5rem',
                border: '1px solid var(--color-border)',
                borderRadius: '8px',
                backgroundColor: 'var(--color-background-secondary)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'start'
              }}
            >
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
                  <h4 style={{ margin: 0, color: 'var(--color-primary)' }}>
                    {playlist.title}
                  </h4>
                  <span style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: 'var(--font-size-xs)',
                    backgroundColor: playlist.is_active ? '#d4edda' : '#f8d7da',
                    color: playlist.is_active ? '#155724' : '#721c24'
                  }}>
                    {playlist.is_active ? 'Activa' : 'Inactiva'}
                  </span>
                </div>
                
                {playlist.description && (
                  <p style={{
                    margin: '0 0 1rem 0',
                    fontSize: 'var(--font-size-sm)',
                    color: 'var(--color-text-secondary)',
                    lineHeight: '1.4'
                  }}>
                    {playlist.description}
                  </p>
                )}
                
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '0.5rem',
                  fontSize: 'var(--font-size-sm)'
                }}>
                  <div>
                    <strong>Playlist ID:</strong> {playlist.youtube_playlist_id}
                  </div>
                  <div>
                    <strong>Videos:</strong> {playlist.video_count || 0}
                  </div>
                  <div>
                    <strong>Usuarios con acceso:</strong> {playlist.active_users_with_access || 0}
                  </div>
                  <div>
                    <strong>Creada:</strong> {new Date(playlist.created_at).toLocaleDateString('es-ES')}
                  </div>
                </div>
              </div>
              
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', marginLeft: '1rem' }}>
                <ActionButton onClick={() => handleManageAccess(playlist)}>
                  Gestionar Acceso
                </ActionButton>
                <ActionButton onClick={() => handleEditPlaylist(playlist)}>
                  Editar
                </ActionButton>
                <a
                  href={`https://www.youtube.com/playlist?list=${playlist.youtube_playlist_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#ff0000',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: 'var(--font-size-sm)',
                    textAlign: 'center'
                  }}
                >
                  Ver en YouTube
                </a>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Access Management Modal */}
      {showAccessModal && selectedPlaylist && (
        <Modal>
          <ModalContent style={{ maxWidth: '800px' }}>
            <ModalHeader>
              <ModalTitle>
                Gestionar Acceso - {selectedPlaylist.title}
              </ModalTitle>
            </ModalHeader>
            <ModalBody>
              <div style={{ marginBottom: '2rem' }}>
                <h4>Usuarios con acceso ({playlistUsers.length})</h4>
                {playlistUsers.length === 0 ? (
                  <p style={{ color: 'var(--color-text-secondary)' }}>
                    Ningún usuario tiene acceso a esta playlist
                  </p>
                ) : (
                  <div style={{ display: 'grid', gap: '0.5rem', marginBottom: '1rem' }}>
                    {playlistUsers.map((userAccess) => (
                      <div
                        key={userAccess.user_id}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.75rem',
                          border: '1px solid var(--color-border)',
                          borderRadius: '4px',
                          backgroundColor: userAccess.is_active ? 'var(--color-background)' : '#f8f9fa'
                        }}
                      >
                        <div>
                          <strong>
                            {userAccess.first_name && userAccess.last_name
                              ? `${userAccess.first_name} ${userAccess.last_name}`
                              : userAccess.email}
                          </strong>
                          <div style={{ fontSize: 'var(--font-size-sm)', color: 'var(--color-text-secondary)' }}>
                            {userAccess.email} • Otorgado: {new Date(userAccess.granted_at).toLocaleDateString('es-ES')}
                            {userAccess.expires_at && ` • Expira: ${new Date(userAccess.expires_at).toLocaleDateString('es-ES')}`}
                          </div>
                        </div>
                        <ActionButton
                          onClick={() => handleRevokeAccess(userAccess.user_id)}
                          style={{ backgroundColor: '#ff6b6b', color: 'white' }}
                        >
                          Revocar
                        </ActionButton>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <h4>Otorgar acceso a nuevos usuarios</h4>
                <div style={{ display: 'grid', gap: '0.5rem' }}>
                  {users
                    .filter(user => !playlistUsers.some(pu => pu.user_id === user.id))
                    .map((user) => (
                      <div
                        key={user.id}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          padding: '0.75rem',
                          border: '1px solid var(--color-border)',
                          borderRadius: '4px'
                        }}
                      >
                        <div>
                          <strong>
                            {user.first_name && user.last_name
                              ? `${user.first_name} ${user.last_name}`
                              : user.email}
                          </strong>
                          <div style={{ fontSize: 'var(--font-size-sm)', color: 'var(--color-text-secondary)' }}>
                            {user.email}
                          </div>
                        </div>
                        <ActionButton onClick={() => handleGrantAccess(user.id)}>
                          Otorgar Acceso
                        </ActionButton>
                      </div>
                    ))}
                </div>
                {users.filter(user => !playlistUsers.some(pu => pu.user_id === user.id)).length === 0 && (
                  <p style={{ color: 'var(--color-text-secondary)' }}>
                    Todos los usuarios ya tienen acceso a esta playlist
                  </p>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <CancelButton onClick={() => setShowAccessModal(false)}>
                Cerrar
              </CancelButton>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* Edit Playlist Modal */}
      {showEditModal && selectedPlaylist && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Editar Playlist - {selectedPlaylist.title}
              </ModalTitle>
            </ModalHeader>
            <form onSubmit={handleUpdatePlaylist}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="title">Título</Label>
                  <Input
                    type="text"
                    id="title"
                    name="title"
                    value={editForm.title}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="description">Descripción</Label>
                  <textarea
                    id="description"
                    name="description"
                    value={editForm.description}
                    onChange={handleInputChange}
                    rows="4"
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      border: '1px solid var(--color-border)',
                      borderRadius: '4px',
                      fontSize: 'var(--font-size-base)',
                      fontFamily: 'inherit'
                    }}
                  />
                </FormGroup>
                <FormGroup>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={editForm.isActive}
                      onChange={handleInputChange}
                    />
                    <Label htmlFor="isActive">Playlist activa</Label>
                  </div>
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowEditModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Guardar Cambios
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
};

export default PlaylistManagement;
