const { Pool } = require('pg');

// Initialize a shared connection pool with fallback configuration
let poolConfig;

if (process.env.DATABASE_URL) {
	// Try using connection string first
	poolConfig = {
		connectionString: process.env.DATABASE_URL,
		ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
	};
} else {
	// Fallback to individual parameters
	poolConfig = {
		host: process.env.DB_HOST || 'localhost',
		port: process.env.DB_PORT || 5432,
		database: process.env.DB_NAME || 'zonacero',
		user: process.env.DB_USER || 'postgres',
		password: process.env.DB_PASSWORD,
		ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
	};
}

const pool = new Pool(poolConfig);

pool.on('error', (err) => {
	console.error('Unexpected error on idle PostgreSQL client', err);
});

async function query(text, params) {
	return pool.query(text, params);
}

module.exports = {
	query,
};


