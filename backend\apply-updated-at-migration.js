const path = require('path');
const fs = require('fs');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const db = require('./db');

async function applyUpdatedAtMigration() {
    console.log('🚀 Applying updated_at migration to user_playlists table...\n');
    
    if (!process.env.DATABASE_URL) {
        console.error('ERROR: DATABASE_URL is not set in backend/.env');
        process.exit(1);
    }

    try {
        // First, check if the column already exists
        console.log('🔍 Checking if updated_at column already exists...');
        const columnCheck = await db.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'user_playlists' 
            AND column_name = 'updated_at'
            AND table_schema = 'public'
        `);

        if (columnCheck.rowCount > 0) {
            console.log('✅ updated_at column already exists! No migration needed.');
            process.exit(0);
        }

        console.log('❌ updated_at column is missing. Applying migration...\n');

        // Read and execute the migration
        const migrationPath = path.join(__dirname, 'migrations', 'add-updated-at-to-user-playlists.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        console.log('📋 Executing migration SQL...');
        console.log('Migration content:');
        console.log('---');
        console.log(migrationSQL);
        console.log('---\n');
        
        await db.query(migrationSQL);
        
        console.log('✅ Migration applied successfully!\n');

        // Verify the column was added
        console.log('🔍 Verifying the migration...');
        const verifyCheck = await db.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'user_playlists' 
            AND column_name = 'updated_at'
            AND table_schema = 'public'
        `);

        if (verifyCheck.rowCount > 0) {
            const col = verifyCheck.rows[0];
            console.log(`✅ updated_at column successfully added: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
        } else {
            console.log('❌ Verification failed: updated_at column not found after migration');
            process.exit(1);
        }

        // Check if trigger was created
        console.log('\n🔧 Checking if trigger was created...');
        const triggerCheck = await db.query(`
            SELECT trigger_name 
            FROM information_schema.triggers 
            WHERE event_object_table = 'user_playlists'
            AND trigger_name = 'set_timestamp_user_playlists'
        `);

        if (triggerCheck.rowCount > 0) {
            console.log('✅ set_timestamp_user_playlists trigger successfully created');
        } else {
            console.log('❌ Warning: set_timestamp_user_playlists trigger not found');
        }

        console.log('\n🎉 Migration completed successfully!');
        console.log('The revoke playlist access endpoint should now work without errors.');
        
        process.exit(0);
    } catch (err) {
        console.error('❌ Migration failed:');
        console.error('Error:', err.message);
        console.error('Code:', err.code);
        if (err.detail) console.error('Detail:', err.detail);
        process.exit(1);
    }
}

applyUpdatedAtMigration();
