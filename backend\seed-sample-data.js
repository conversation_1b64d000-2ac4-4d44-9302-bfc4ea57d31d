const db = require('./db');

// TEST: Seed sample data for development without YouTube credentials
async function seedSampleData() {
    try {
        console.log('TEST: Seeding sample course data...');

        // Insert a sample course using a public YouTube playlist
        const sampleCourse = {
            slug: 'test-react-tutorial-course',
            title: 'TEST - React Tutorial Course',
            description: 'Complete React tutorial series - TEST data for development without YouTube credentials',
            youtube_playlist_id: 'PL4cUxeGkcC9gZD-Tvwfod2gaISzfRiP9', // Traversy Media React Tutorial
            youtube_playlist_url: 'https://www.youtube.com/playlist?list=PL4cUxeGkcC9gZD-Tvwfod2gaISzfRiP9',
            youtube_channel_title: 'Traversy Media',
            youtube_video_count: 12,
            youtube_thumbnail_url: 'https://i.ytimg.com/vi/hQAHSlTtcmY/hqdefault.jpg'
        };

        // Check if course already exists
        const existingCourse = await db.query(
            'SELECT id FROM courses WHERE slug = $1',
            [sampleCourse.slug]
        );

        if (existingCourse.rowCount > 0) {
            console.log('TEST: Sample course already exists, skipping...');
            return;
        }

        // Insert the sample course
        const result = await db.query(
            `INSERT INTO courses (
                slug, title, description, youtube_playlist_id, youtube_playlist_url,
                youtube_channel_title, youtube_video_count, youtube_thumbnail_url, synced_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW()) RETURNING id, title`,
            [
                sampleCourse.slug,
                sampleCourse.title,
                sampleCourse.description,
                sampleCourse.youtube_playlist_id,
                sampleCourse.youtube_playlist_url,
                sampleCourse.youtube_channel_title,
                sampleCourse.youtube_video_count,
                sampleCourse.youtube_thumbnail_url
            ]
        );

        console.log('TEST: Sample course inserted:', result.rows[0]);

        // Get admin user ID to grant access to the course
        const adminUser = await db.query(
            'SELECT id FROM users WHERE role = $1 LIMIT 1',
            ['admin']
        );

        if (adminUser.rowCount > 0) {
            // Grant admin access to the course
            await db.query(
                'INSERT INTO user_courses (user_id, course_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
                [adminUser.rows[0].id, result.rows[0].id]
            );
            console.log('TEST: Granted admin access to sample course');
        }

        console.log('TEST: Sample data seeding completed successfully');

    } catch (error) {
        console.error('TEST: Error seeding sample data:', error);
        throw error;
    }
}

// Run if called directly
if (require.main === module) {
    seedSampleData()
        .then(() => process.exit(0))
        .catch((err) => {
            console.error(err);
            process.exit(1);
        });
}

module.exports = seedSampleData;
