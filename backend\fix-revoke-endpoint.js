const path = require('path');
const fs = require('fs');

async function fixRevokeEndpoint() {
    console.log('🔧 Fixing revoke playlist access endpoint...\n');
    
    const serverPath = path.join(__dirname, 'server.js');
    
    try {
        // Read the current server.js file
        let serverContent = fs.readFileSync(serverPath, 'utf8');
        
        // Find the problematic UPDATE query
        const problematicQuery = `UPDATE user_playlists
				SET is_active = false, updated_at = NOW()
				WHERE user_id = $1 AND playlist_id = $2 AND is_active = true
				RETURNING id, user_id, playlist_id, is_active`;
        
        // Replace with a query that doesn't use updated_at
        const fixedQuery = `UPDATE user_playlists
				SET is_active = false
				WHERE user_id = $1 AND playlist_id = $2 AND is_active = true
				RETURNING id, user_id, playlist_id, is_active`;
        
        if (serverContent.includes('updated_at = NOW()')) {
            console.log('🎯 Found problematic query with updated_at = NOW()');
            
            // Replace the query
            serverContent = serverContent.replace(
                /SET is_active = false, updated_at = NOW\(\)/g,
                'SET is_active = false'
            );
            
            // Write the fixed content back
            fs.writeFileSync(serverPath, serverContent, 'utf8');
            
            console.log('✅ Successfully removed updated_at = NOW() from revoke endpoint');
            console.log('📝 The endpoint will now work without the updated_at column');
            console.log('\n⚠️  Note: This is a temporary fix. The proper solution is to apply the migration.');
        } else {
            console.log('ℹ️  No problematic query found. The server.js may already be fixed.');
        }
        
    } catch (err) {
        console.error('❌ Failed to fix server.js:');
        console.error('Error:', err.message);
        process.exit(1);
    }
}

fixRevokeEndpoint();
