const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const db = require('./db');

async function debugPlaylistAccess() {
    console.log('🔍 Debugging Playlist Access Issue...\n');
    
    try {
        // 1. Check database connection
        console.log('1. Testing database connection...');
        const timeResult = await db.query('SELECT NOW() as current_time');
        console.log('✅ Database connected. Current time:', timeResult.rows[0].current_time);
        
        // 2. Check users table structure and data
        console.log('\n2. Checking users table...');
        const usersResult = await db.query(`
            SELECT id, email, first_name, last_name, role, created_at 
            FROM users 
            WHERE role != 'admin' 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        console.log('Users found:', usersResult.rowCount);
        console.log('Sample users:');
        usersResult.rows.forEach((user, index) => {
            console.log(`  ${index + 1}. ID: ${user.id} (${typeof user.id})`);
            console.log(`     Email: ${user.email}`);
            console.log(`     Name: ${user.first_name} ${user.last_name}`);
            console.log(`     Role: ${user.role}`);
            console.log('');
        });
        
        // 3. Check playlists table structure and data
        console.log('3. Checking playlists table...');
        const playlistsResult = await db.query(`
            SELECT id, youtube_playlist_id, title, is_active, created_at 
            FROM playlists 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        console.log('Playlists found:', playlistsResult.rowCount);
        console.log('Sample playlists:');
        playlistsResult.rows.forEach((playlist, index) => {
            console.log(`  ${index + 1}. ID: ${playlist.id} (${typeof playlist.id})`);
            console.log(`     YouTube ID: ${playlist.youtube_playlist_id}`);
            console.log(`     Title: ${playlist.title}`);
            console.log(`     Active: ${playlist.is_active}`);
            console.log('');
        });
        
        // 4. Check user_playlists table
        console.log('4. Checking user_playlists table...');
        const userPlaylistsResult = await db.query(`
            SELECT 
                up.id,
                up.user_id,
                up.playlist_id,
                up.is_active,
                up.granted_at,
                u.email as user_email,
                p.title as playlist_title
            FROM user_playlists up
            JOIN users u ON up.user_id = u.id
            JOIN playlists p ON up.playlist_id = p.id
            ORDER BY up.granted_at DESC
            LIMIT 10
        `);
        console.log('User-playlist relationships found:', userPlaylistsResult.rowCount);
        console.log('Sample relationships:');
        userPlaylistsResult.rows.forEach((rel, index) => {
            console.log(`  ${index + 1}. User: ${rel.user_email} (ID: ${rel.user_id})`);
            console.log(`     Playlist: ${rel.playlist_title} (ID: ${rel.playlist_id})`);
            console.log(`     Active: ${rel.is_active}`);
            console.log(`     Granted: ${rel.granted_at}`);
            console.log('');
        });
        
        // 5. Test specific IDs from the error
        console.log('5. Testing specific IDs from the error...');
        const testPlaylistId = '34146279-4e57-4e7b-886c-3ecd0c7b0ebc';
        const testUserId = '904fc89d-a760-46f5-afcb-080d94925b0a';
        
        console.log(`Testing Playlist ID: ${testPlaylistId}`);
        const playlistCheck = await db.query('SELECT id, title FROM playlists WHERE id = $1', [testPlaylistId]);
        console.log('Playlist check result:', playlistCheck.rows);
        
        console.log(`Testing User ID: ${testUserId}`);
        const userCheck = await db.query('SELECT id, email FROM users WHERE id = $1', [testUserId]);
        console.log('User check result:', userCheck.rows);
        
        // 6. Check if there's an existing relationship
        console.log('\n6. Checking existing relationship...');
        const relationshipCheck = await db.query(`
            SELECT * FROM user_playlists 
            WHERE user_id = $1 AND playlist_id = $2
        `, [testUserId, testPlaylistId]);
        console.log('Existing relationship:', relationshipCheck.rows);
        
        console.log('\n✅ Debug complete!');
        
    } catch (err) {
        console.error('❌ Debug failed:', err);
        console.error('Error details:', {
            message: err.message,
            code: err.code,
            detail: err.detail
        });
    } finally {
        process.exit(0);
    }
}

debugPlaylistAccess();
