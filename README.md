# ZonaCero Portal

A full-stack web application for ZonaCero course management with authentication and admin features.

## Tech Stack

- **Frontend**: React 19, React Router, Styled Components
- **Backend**: Node.js, Express, PostgreSQL
- **Authentication**: JWT with httpOnly cookies

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v14 or higher)
- [PostgreSQL](https://www.postgresql.org/download/) (v12 or higher)
- npm (comes with Node.js)

## Setup (First Time Only)

### 1. Clone and Install Dependencies

```bash
# Clone the repository (if you haven't already)
cd zonacero

# Install dependencies
npm install
```

### 2. Set Up PostgreSQL Database

Create a database for the project:

```bash
# Connect to PostgreSQL
psql -U postgres

# Create the database
CREATE DATABASE zonacero;

# Exit psql
\q
```

### 3. Configure Environment Variables

#### Backend Configuration

Create a `backend/.env` file with the following content:

```env
# Server Configuration
PORT=4000
NODE_ENV=development

# Database Connection
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/zonacero

# JWT Security
JWT_SECRET=your-secret-key-change-in-production
COOKIE_NAME=token

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# YouTube Data API v3 (for backend test endpoints)
YOUTUBE_API_KEY=your-youtube-api-key-here

# Optional: Auto-create admin account on startup
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=changeme
```

#### Frontend Configuration

Create a `.env` file in the project root with the following content:

```env
# YouTube Data API v3 Key (for frontend direct API calls)
REACT_APP_YOUTUBE_API_KEY=your-youtube-api-key-here

# Backend API URL (usually not needed if using proxy)
REACT_APP_API_BASE_URL=http://localhost:4000
```

#### YouTube API Setup

To enable YouTube playlist functionality:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3
4. Create credentials (API Key)
5. Add the API key to both `.env` files as shown above

**Note**: Update the `DATABASE_URL` if your PostgreSQL credentials are different from the defaults.

### 4. Initialize the Database (One-Time Setup)

Run the schema and migrations to set up the database tables:

```bash
# Run the base schema
npm run run-schema

# Run migrations (adds role column, courses, and user_courses tables)
npm run run-migration
```

**Note**: You only need to run these commands ONCE during initial setup. They use `IF NOT EXISTS` clauses, so they're safe to run multiple times, but it's not necessary.

## Running the Project

After the initial setup, you only need to start the servers:

You'll need two terminals - one for the backend server and one for the frontend.

**Terminal 1 - Backend Server:**
```bash
npm run server
```

The backend will start on `http://localhost:4000`

**Terminal 2 - Frontend:**
```bash
npm start
```

The frontend will start on `http://localhost:3000`

## Available Scripts

### Frontend Scripts

- `npm start` - Start the development server (port 3000)
- `npm run build` - Build for production
- `npm test` - Run tests

### Backend Scripts

- `npm run server` - Start the backend API server (port 4000)
- `npm run run-schema` - Initialize database schema
- `npm run run-migration` - Run database migrations
- `npm run test-db` - Test database connection

## Project Structure

```
zonacero/
├── backend/              # Backend API server
│   ├── server.js        # Express server and routes
│   ├── db.js            # Database connection
│   ├── schema.sql       # Base database schema
│   ├── migrations/      # Database migration files
│   └── .env            # Backend environment variables
├── src/                 # Frontend React application
│   ├── components/     # React components
│   ├── pages/          # Page components
│   ├── contexts/       # React contexts (Auth)
│   └── services/       # API service functions
└── public/             # Static assets
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/me` - Get current user info

### Admin Only

- `GET /api/admin/users` - List all users
- `GET /api/admin/courses` - List all courses
- `POST /api/admin/courses` - Create a new course
- `DELETE /api/admin/courses/:id` - Delete a course
- `POST /api/admin/users/:userId/courses/:courseId` - Grant course access
- `DELETE /api/admin/users/:userId/courses/:courseId` - Revoke course access

### User

- `GET /api/my/courses` - Get courses available to current user

### Health Check

- `GET /api/health` - Server health check

## Admin Account

To create an admin account, either:

1. **Auto-bootstrap on startup**: Add to `backend/.env`:
   ```env
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=yourpassword
   ```

2. **Create manually**: After logging in as the bootstrapped admin, use the `/api/auth/create-admin` endpoint.

## Development

The frontend is configured to proxy API requests to the backend during development. The backend API runs on port 4000, while the frontend runs on port 3000.

Both servers support hot-reloading, so changes to code will automatically restart/reload the respective servers.

## Troubleshooting

### Database Connection Issues

1. Ensure PostgreSQL is running
2. Verify your `DATABASE_URL` in `backend/.env` is correct
3. Check database exists: `psql -l` should list `zonacero`
4. Test connection: `npm run test-db`

### Port Already in Use

- Backend (4000): Kill the process or change `PORT` in `backend/.env`
- Frontend (3000): Kill the process or the terminal will prompt you to use a different port

### Admin Login Issues

- Verify `ADMIN_EMAIL` and `ADMIN_PASSWORD` are set in `backend/.env`
- Check server logs for admin bootstrap messages
- If admin exists, it won't be recreated (check logs)

## License

Private project - All rights reserved
