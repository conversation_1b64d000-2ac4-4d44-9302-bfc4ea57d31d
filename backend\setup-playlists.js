const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const fs = require('fs');
const db = require('./db');
const { seedPlaylists } = require('./seed-playlists');

async function setupPlaylists() {
    try {
        console.log('🚀 Setting up playlist management system...\n');

        // 1. Run the playlist management migration
        console.log('📋 Running playlist management migration...');
        const migrationPath = path.join(__dirname, 'migrations', 'add-playlist-management.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        await db.query(migrationSQL);
        console.log('✅ Migration completed successfully\n');

        // 2. Seed the playlists
        console.log('🌱 Seeding playlists...');
        await seedPlaylists();
        console.log('✅ Playlists seeded successfully\n');

        // 3. Show summary
        console.log('📊 Setup Summary:');
        const playlistCount = await db.query('SELECT COUNT(*) as total FROM playlists');
        const userCount = await db.query('SELECT COUNT(*) as total FROM users WHERE role = $1', ['user']);
        
        console.log(`   • Total playlists: ${playlistCount.rows[0].total}`);
        console.log(`   • Total users: ${userCount.rows[0].total}`);
        console.log(`   • Admin users can now manage playlist access via the admin dashboard`);
        console.log(`   • Regular users will see their assigned playlists in the dashboard\n`);

        console.log('🎉 Playlist management system setup completed!');
        console.log('\nNext steps:');
        console.log('1. Start the server: npm run server');
        console.log('2. Login as admin to manage playlist access');
        console.log('3. Assign playlists to users via the admin dashboard');

    } catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    setupPlaylists()
        .then(() => {
            console.log('\n✨ Setup completed successfully!');
            process.exit(0);
        })
        .catch(err => {
            console.error('\n💥 Setup failed:', err);
            process.exit(1);
        });
}

module.exports = { setupPlaylists };
