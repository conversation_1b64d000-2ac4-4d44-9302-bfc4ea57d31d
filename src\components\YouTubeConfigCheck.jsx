import React, { useState, useEffect } from 'react';
import youtubeService from '../services/youtubeService';

const YouTubeConfigCheck = ({ onConfigChange }) => {
  const [configStatus, setConfigStatus] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    checkConfiguration();
  }, []);

  const checkConfiguration = () => {
    const status = youtubeService.getConfigStatus();
    setConfigStatus(status);
    setIsVisible(!status.isConfigured);
    
    if (onConfigChange) {
      onConfigChange(status.isConfigured);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      backgroundColor: '#fff3cd',
      border: '1px solid #ffeaa7',
      borderRadius: '8px',
      padding: '16px',
      maxWidth: '400px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      zIndex: 1000
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '12px'
      }}>
        <h4 style={{ margin: 0, color: '#856404' }}>
          ⚠️ YouTube API Configuration
        </h4>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            color: '#856404'
          }}
        >
          ×
        </button>
      </div>
      
      <div style={{ color: '#856404', fontSize: '14px', lineHeight: '1.4' }}>
        <p style={{ margin: '0 0 8px 0' }}>
          YouTube API key is not configured. To enable playlist functionality:
        </p>
        <ol style={{ margin: '0 0 12px 0', paddingLeft: '20px' }}>
          <li>Get an API key from <a href="https://console.developers.google.com/" target="_blank" rel="noopener noreferrer">Google Cloud Console</a></li>
          <li>Enable YouTube Data API v3</li>
          <li>Add <code>REACT_APP_YOUTUBE_API_KEY=your-key</code> to your .env file</li>
          <li>Restart the development server</li>
        </ol>
        
        <div style={{ fontSize: '12px', opacity: 0.8 }}>
          Current API key: {configStatus?.apiKey || 'Not set'}
        </div>
        
        <button
          onClick={checkConfiguration}
          style={{
            marginTop: '8px',
            padding: '4px 8px',
            fontSize: '12px',
            backgroundColor: '#856404',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Recheck Configuration
        </button>
      </div>
    </div>
  );
};

export default YouTubeConfigCheck;
