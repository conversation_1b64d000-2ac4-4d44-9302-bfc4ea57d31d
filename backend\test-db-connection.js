const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });
const { Pool } = require('pg');

async function testConnection() {
    console.log('🔍 Testing database connection...\n');
    
    // Parse the DATABASE_URL manually to handle special characters
    const dbUrl = process.env.DATABASE_URL;
    console.log('Database URL:', dbUrl);
    
    try {
        // Method 1: Try with connectionString
        console.log('📡 Attempting connection with connectionString...');
        const pool1 = new Pool({
            connectionString: dbUrl,
            ssl: false
        });
        
        const client1 = await pool1.connect();
        console.log('✅ Connection successful with connectionString');
        
        // Test a simple query
        const result = await client1.query('SELECT NOW() as current_time');
        console.log('⏰ Current database time:', result.rows[0].current_time);
        
        // Check if tables exist
        const tablesResult = await client1.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);
        
        console.log('\n📋 Existing tables:');
        if (tablesResult.rows.length === 0) {
            console.log('   No tables found. You may need to run the schema first.');
        } else {
            tablesResult.rows.forEach(row => {
                console.log(`   • ${row.table_name}`);
            });
        }
        
        client1.release();
        await pool1.end();
        
        console.log('\n🎉 Database connection test completed successfully!');
        return true;
        
    } catch (error) {
        console.error('❌ Connection failed:', error.message);
        
        // Method 2: Try with individual connection parameters
        console.log('\n🔄 Trying alternative connection method...');
        
        try {
            // Parse URL manually
            const url = new URL(dbUrl);
            const pool2 = new Pool({
                host: url.hostname,
                port: url.port || 5432,
                database: url.pathname.slice(1), // Remove leading slash
                user: url.username,
                password: url.password,
                ssl: false
            });
            
            const client2 = await pool2.connect();
            console.log('✅ Connection successful with individual parameters');
            
            const result = await client2.query('SELECT NOW() as current_time');
            console.log('⏰ Current database time:', result.rows[0].current_time);
            
            client2.release();
            await pool2.end();
            
            console.log('\n🎉 Alternative connection method successful!');
            return true;
            
        } catch (error2) {
            console.error('❌ Alternative connection also failed:', error2.message);
            
            console.log('\n🔧 Troubleshooting suggestions:');
            console.log('1. Make sure PostgreSQL is running');
            console.log('2. Check if the database "zonacero" exists');
            console.log('3. Verify the username and password are correct');
            console.log('4. Ensure the PostgreSQL service is accepting connections on port 5432');
            console.log('5. Try connecting manually with: psql -h localhost -p 5432 -U postgres -d zonacero');
            
            return false;
        }
    }
}

if (require.main === module) {
    testConnection()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((err) => {
            console.error('💥 Test failed:', err);
            process.exit(1);
        });
}

module.exports = { testConnection };
